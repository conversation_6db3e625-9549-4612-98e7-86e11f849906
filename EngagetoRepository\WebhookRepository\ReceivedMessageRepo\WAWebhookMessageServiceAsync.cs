﻿using Engageto.Hubs;
using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.OptinContracts;
using EngagetoContracts.Services;
using EngagetoContracts.UserContracts;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoDapper.Data.Dtos;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace EngagetoRepository.WebhookRepository.ReceivedMessageRepo
{
    public class WAWebhookMessageServiceAsync : IWAWebhookMessageServiceAsync
    {
        private readonly IGenericRepository _genericRepository;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IConversationService _conversationService;
        private readonly IMediaURL _mediaURL;
        private readonly IOptin _optin;
        private readonly IWebhookService _webhookService;
        private readonly IWAWebhookHelper _wAWebhookHelper;
        private readonly IJobService _jobService;
        private readonly ApplicationDbContext _applicationDbContext;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService _conversationAnalyticsService;
        private IHubContext<MessageHub, IMessageHubClient> _messageHub;
        private readonly IWAAutoReplyMessageProcess _wAAutoReplyMessage;
        private readonly LeadProcessingService _leadProcessingService;
        private readonly IAutomationSettingService _automationSettingService;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IClientDetailsService _clientDetailsService;
        private readonly IEmailService _emailService;
        private readonly INodeWorkflowEngineService _nodeWorkflowEngineService;
        private readonly ILeadgenIntegrationService _leadgenIntegrationService;
        private readonly IInboxRepository _inboxRepository;


        public WAWebhookMessageServiceAsync(IGenericRepository genericRepository,
            IContactRepositoryBase contactRepository,
            IConversationService conversationService,
            ApplicationDbContext applicationDbContext,
            IMediaURL mediaURL,
            IOptin optin,
            IJobService jobService,
            IWebhookService webhookService,
            IWAWebhookHelper wAWebhookHelper,
            IHubContext<MessageHub, IMessageHubClient> messageHub,
            IWAAutoReplyMessageProcess wAAutoReplyMessage,
            LeadProcessingService leadProcessingService,
            IAutomationSettingService automationSettingService,
            ILogHistoryService logHistoryService,
            IClientDetailsService clientDetailsService,
            IEmailService emailService,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
            EngagetoDapper.Data.Interfaces.IConversationAnalyticsInterfaces.IConversationAnalyticsService conversationAnalyticsService,
            INodeWorkflowEngineService nodeWorkflowEngineService,
            ILeadgenIntegrationService leadgenIntegrationService,
            IInboxRepository inboxRepository
            )
        {
            _applicationDbContext = applicationDbContext;
            _genericRepository = genericRepository;
            _conversationService = conversationService;
            _contactRepository = contactRepository;
            _mediaURL = mediaURL;
            _optin = optin;
            _jobService = jobService;
            _webhookService = webhookService;
            _wAWebhookHelper = wAWebhookHelper;
            _wAAutoReplyMessage = wAAutoReplyMessage;
            _userService = userService;
            _messageHub = messageHub;
            _leadProcessingService = leadProcessingService;
            _automationSettingService = automationSettingService;
            _logHistoryService = logHistoryService;
            _clientDetailsService = clientDetailsService;
            _conversationAnalyticsService = conversationAnalyticsService;
            _emailService = emailService;
            _nodeWorkflowEngineService = nodeWorkflowEngineService;
            _leadgenIntegrationService = leadgenIntegrationService;
            _inboxRepository = inboxRepository;
        }
        public async Task ProcessWAWebhookMessageAsync(WAWebhookDto webhookDto, CancellationToken token)
        {
            try
            {
                var changes = webhookDto.Entry?[0].Changes?[0];
                var entryValue = webhookDto.Entry?[0];

                var metaData = webhookDto.Entry?[0].Changes?[0].Value?.Metadata;
                var contactDetails = webhookDto.Entry?[0]?.Changes?[0]?.Value?.Contacts?.FirstOrDefault();
                var message = webhookDto.Entry?[0]?.Changes?[0]?.Value?.Messages?.FirstOrDefault();
                var field = webhookDto.Entry?[0]?.Changes[0]?.Field;
                switch (field)
                {
                    case "messages":
                        // getting the businessId 
                        if (contactDetails != null)
                        {
                            await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessWAWebhookMessageAsync:ProcessSentMessageAsync", webhookDto, contactDetails, "process webhook");
                            await RunInBackground(() => HandleReceviedMessageAsync(webhookDto, token));
                        }
                        if (changes?.Value?.Statuses != null)
                        {
                            await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessWAWebhookMessageAsync:ProcessSentMessageAsync", webhookDto, "messages", "process webhook");
                            await RunInBackground(() => ProcessSentMessageAsync(webhookDto, token));
                            await ProcessSentMessageAsync(webhookDto, token);
                        }
                        break;
                    case "message_template_status_update":
                        await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessWAWebhookMessageAsync:message_template_status_update", webhookDto, "template", "process webhook");
                        await RunInBackground(() => HandleReceivedTemplateStatus(webhookDto, "message_template_status_update", token));
                        break;
                    case "business_capability_update":
                        var waAccountId = entryValue?.Id;
                        long messageLimit = entryValue?.Changes?[0].Value?.MessageLimitPerUser ?? 0;
                        if (waAccountId != null && messageLimit > 0)
                        {
                            await _clientDetailsService.UpdateMetaAccountByWebhookAsync(waAccountId, messageLimit, null, null, null);
                        }
                        break;
                    case "phone_number_quality_update":
                        waAccountId = entryValue?.Id;
                        messageLimit = 0;
                        string? businessStatus = entryValue?.Changes?[0].Value?.Event;
                        string? tier = entryValue?.Changes?[0].Value?.CurrentLimit;
                        string? displayNumber = entryValue?.Changes?[0].Value?.DisplayPhoneNumber;
                        if (waAccountId != null && businessStatus != null && tier != null && displayNumber != null)
                        {
                            await _clientDetailsService.UpdateMetaAccountByWebhookAsync(waAccountId, messageLimit, businessStatus, tier, displayNumber);
                        }
                        break;
                    case "message_template_quality_update":
                        await RunInBackground(() => HandleReceivedTemplateStatus(webhookDto, "message_template_status_update", token));
                        break;

                }

            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("ProcessWAWebhookMessageAsync:Error", webhookDto, JsonConvert.SerializeObject(ex), "process webhook");
                Console.WriteLine(ex.Message);
            }
        }

        public async Task ProcessSentMessageAsync(WAWebhookDto message, CancellationToken token)
        {
            try
            {
                var value = message.Entry?[0].Changes?[0]?.Value;

                if (value?.Statuses != null)
                {
                    var status = value.Statuses.FirstOrDefault()?.Status;
                    var waMessageId = value.Statuses.FirstOrDefault()?.Id;
                    var updatedValues = new Dictionary<string, object>();

                    var metaData = value?.Metadata;
                    //var conditions = new Dictionary<string, object>() { { "WhatsAppMessageId", waMessageId } };
                    //var users = await _userService.GetUsersByBusinessIdAync(null, metaData.PhoneNumberId);

                    //var businessId = users?.FirstOrDefault()?.BusinessId;
                    string @event = string.Empty;
                    //List<RequestFilterDto> requestFilters = new() { new() { Key = "WhatsAppMessageId", Value = waMessageId, Operator = "=" } };
                    //var columns = StringHelper.GetPropertyNames<Conversations>();
                    //var conversation = (await _genericRepository.GetRecordByRequestFilter<Conversations>(requestFilters, "Conversations", 0, 0, columns)).FirstOrDefault();
                    bool isCampaign = await _genericRepository.IsExistAsync<CampaignTracker>(new() { { nameof(CampaignTracker.WhatsAppMessagesId), waMessageId } }, nameof(CampaignTracker));
                    Conversations conversation = new();
                    switch (status)
                    {
                        case "sent":
                            @event = "UpdateConversationSentMessage";
                            conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(waMessageId, ConvStatus.sent);
                            break;
                        case "delivered":
                            @event = "UpdateConversationDeliveredMessage";
                            conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(waMessageId, ConvStatus.delivered);
                            //await _conversationAnalyticsService.SaveConversationCostDetuctionHistoryAsync(new List<Conversations> { conversation }, businessId, conversations.UserId);
                            #region Lead notes updating for delivered message  
                            if (!isCampaign)
                            {
                                await _leadgenIntegrationService.ProcessIntegrationForLeadAsync(new IntegrationAccountRecord(conversation.From ?? string.Empty, null, conversation, null, null, new List<IntegrationEvent> { IntegrationEvent.Delivered }));
                            }
                            #endregion
                            break;
                        case "read":
                            @event = "UpdateConversationReadMessage";
                            conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(waMessageId, ConvStatus.read);
                            break;
                        case "failed":
                            @event = "UpdateConversationFaildMessage";
                            var errorMessage = value.Statuses.FirstOrDefault()?.Errors?.FirstOrDefault()?.ErrorData?.Details;
                            var error = JsonConvert.SerializeObject(value.Statuses.FirstOrDefault()?.Errors);
                            conversation = await _inboxRepository.UpdateConversatonAndCampaignTrackerAsync(waMessageId, ConvStatus.failed,errorMessage,error);
                            break;
                    }
                    if (!isCampaign)
                    {
                        await SendConvMessageOnServer(conversation.From, new List<Conversations> { conversation }, false);
                        await _webhookService.SaveWebhookEventsAsync(conversation.From, @event, conversation ?? new());
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        #region Helper Method
        private async Task RunInBackground(Func<Task> action)
        {
            try
            {
                await Task.Run(action);
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("RunInBackground:Error", JsonConvert.SerializeObject(action), JsonConvert.SerializeObject(ex), "action");
                Console.WriteLine(ex.Message); ;
            }
        }
        public async Task HandleReceivedTemplateStatus(WAWebhookDto message, string? field, CancellationToken token)
        {
            field = message.Entry?[0]?.Changes[0]?.Field;
            var value = message.Entry[0]?.Changes[0]?.Value;
            var status = value.Event;
            var templateId = value.MessageTemplateId;
            var reasion = value.Reason;
            var template = (await _genericRepository.GetByObjectAsync<Template>(new() { { "MetaId", templateId } }, "Templates"))?.FirstOrDefault();
            if (template != null)
            {
                switch (field)
                {
                    case "message_template_status_update":
                        //template.Error = reasion;
                        template.StatusReason = reasion;
                        switch (status?.ToLowerInvariant())
                        {
                            case string s when s.Contains("approved"):
                                template.Status = WATemplateStatus.APPROVED;
                                await RunInBackground(() => _webhookService.SendTemplateWebhookAsync(template.BusinessId, template.TemplateId));
                                var waTemplate = template.Adapt<WebhookResponseTemplateDto>();
                                await RunInBackground(() => _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord
                                    (
                                        template.BusinessId, null, null, waTemplate, null, new List<IntegrationEvent> { IntegrationEvent.Template }), CancellationToken.None
                                    ));
                                break;
                            case string s when s.Contains("rejected"):
                                template.Status = WATemplateStatus.REJECTED;
                                break;
                            case string s when s.Contains("flagged"):
                                template.Status = WATemplateStatus.FLAGGED;
                                break;
                            case string s when s.Contains("in_appeal"):
                                template.Status = WATemplateStatus.IN_APPEAL;
                                break;
                        }
                        break;

                    case "message_template_quality_update":
                        await _emailService.SendEmailViaUtility("Template Quility", JsonConvert.SerializeObject(value), null, new List<string> { "<EMAIL>", "<EMAIL>" }, null, null);
                        var ratingObject = new QualityScoreDtos
                        {
                            PreviousScore = GetQualityScoreEnum(value.PreviousQualityScore),
                            NewScore = GetQualityScoreEnum(value.NewQualityScore),
                            CreatedAt = DateTime.UtcNow,
                        };

                        var existingRatings = string.IsNullOrEmpty(template.Rating)
                            ? new List<QualityScoreDtos>()
                            : JsonConvert.DeserializeObject<List<QualityScoreDtos>>(template.Rating);

                        existingRatings?.Add(ratingObject);

                        SendQualityRatingEmail(value.PreviousQualityScore, value.NewQualityScore, template.BusinessId);

                        break;
                }
                _applicationDbContext.Templates.Update(template);
                await _applicationDbContext.SaveChangesAsync();
                //await _webhookService.SendTemplateWebhookAsync(template.BusinessId, template.TemplateId);
                //await _genericRepository.UpdateRecordAsync("Templates", StringHelpers.GetPropertyNames<TemplateDto>(), template, new Dictionary<string, object> { { "TemplateId", template.TemplateId } });
            }
        }
        private QualityScore GetQualityScoreEnum(string score)
        {
            return score.ToLower() switch
            {
                "green" => QualityScore.GREEN,
                "yellow" => QualityScore.YELLOW,
                "red" => QualityScore.RED,
                _ => QualityScore.UNKNOWN,
            };
        }
        #region Received Process

        private async Task<Contacts> ProcessContactAsync(EngagetoEntities.Dtos.WebhookDtos.Contact contact, string businessId)
        {
            var countryCodeAndPhoneNumber = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(string.Concat("+", contact.WaId));
            //    var apiKeyEntity =  await _applicationDbContext.ApiKeyEntities.FirstOrDefaultAsync(i=>i.CompanyId== businessId);
            //   var source = apiKeyEntity.Source;

            // var source = await _applicationDbContext.ApiKeyEntities.Where(i => i.CompanyId == businessId).Select(i => i.Source).FirstOrDefaultAsync();
            var source = SourceType.WhatsApp;
            var contactDetails = await _contactRepository.SaveContactNumber(countryCodeAndPhoneNumber.CountryCode, countryCodeAndPhoneNumber.NationalNumber, businessId, source, contact.Profile?.Name);
            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(businessId, contactDetails, null, null, null, new List<IntegrationEvent> { IntegrationEvent.LeadGen }), CancellationToken.None);
            return contactDetails;
        }

        public async Task HandleReceviedMessageAsync(WAWebhookDto webhookDto, CancellationToken token)
        {
            var metaData = webhookDto.Entry?[0].Changes?[0].Value?.Metadata;
            var contactDetails = webhookDto.Entry?[0]?.Changes?[0]?.Value?.Contacts?.FirstOrDefault();
            var message = webhookDto.Entry?[0]?.Changes?[0]?.Value?.Messages?.FirstOrDefault();
            //declare variables
            string? type = message?.Type;
            string textMessage = string.Empty;
            string url = string.Empty;
            string mimeType = string.Empty;
            string caption = string.Empty;
            string? replyId = message?.MessageContext?.Id;

            var businessId = (await _genericRepository.GetByObjectAsync<BusinessDetailsMeta>(new Dictionary<string, object> { { "PhoneNumberID", metaData.PhoneNumberId } }))
                            ?.FirstOrDefault()?.BusinessId;

            if (!(await _genericRepository.IsExistAsync<Conversations>(new Dictionary<string, object> { { "WhatsAppMessageId", message.Id } })))
            {
                if (businessId != null)
                {
                    try
                    {
                        var contact = await ProcessContactAsync(contactDetails, businessId);
                        if (contact != null)
                        {
                            // Use helper method to retrieve URL, mime type, and caption
                            (textMessage, url, mimeType, caption, replyId) = await GetMediaDetailsAsync(textMessage, type, message, metaData.PhoneNumberId, replyId);
                            // Save conversation data based on message type
                            var conv = await _conversationService.SaveTextMediaConversationAsync(textMessage, contactDetails.WaId, businessId.ToLowerInvariant(), type, caption, mimeType, url, message.Id, replyId);
                            conv = conv.Clone() as Conversations;
                            await SendConvMessageOnServer(businessId, new() { conv }, true);
                            conv.Status = ConvStatus.received;
                            #region OptInAndOut process#
                            bool isOptInAndOutResult = await _optin.OptInAndOutKeywordProcessAsync(webhookDto, businessId);
                            #endregion
                            #region delay response
                            if (!string.IsNullOrEmpty(contact?.DelayResponseJobID))
                            {
                                _jobService.Delete(contact.DelayResponseJobID);
                                contact.DelayResponseJobID = null;
                                _applicationDbContext.Contacts.Update(contact);
                                await _applicationDbContext.SaveChangesAsync();
                            }
                            #endregion
                            #region Autoreply operation
                            await RunInBackground(() => _wAAutoReplyMessage.SendAutoReplyMessageAsync(conv, businessId));
                            #endregion

                            if (contact != null)
                            {
                                await RunInBackground(() => _nodeWorkflowEngineService.ProcessWorkflowAsync(contact, textMessage, false));
                            }

                            #region Inbox setting operation
                            if (message.Type != "button" && message.Type != "interactive" && !isOptInAndOutResult)
                            {
                                await RunInBackground(() => _wAWebhookHelper.InboxSettingMessageAsync(businessId, contact.Contact, contact.CountryCode));
                            }
                            #endregion
                            #region ChatStatus update
                            if (contact?.ChatStatus != EngagetoEntities.Enums.ChatStatus.open)
                            {
                                await RunInBackground(() => _wAWebhookHelper.UpdateContactChatStatusAsync(contact));
                            }
                            #endregion
                            #region send conversation event message by webhook
                            await _webhookService.SaveWebhookEventsAsync(businessId, "Received Message", conv);
                            #endregion

                            #region Lead gen processing
                            await _leadProcessingService.SetLeadDataAsync(new IntegrationAccountRecord(businessId, contact, conv, null, null, new List<IntegrationEvent> { IntegrationEvent.OneTimeLeadGen, IntegrationEvent.Received, IntegrationEvent.OneTimeReceived }), token);
                            //await _automationSettingService.SendAutomationMessageAsync(businessId, contact, conv, Guid.Empty);
                            #endregion
                            #region new automation setting
                            //if (message.Type != "button" && message.Type != "interactive" && !isOptInAndOutResult)
                            //{
                            await RunInBackground(() => _automationSettingService.SendAutomationMessageAsync(businessId, contact, conv, null));
                            //}
                            #endregion


                        }
                    }
                    catch (Exception ex)
                    {
                        await _logHistoryService.SaveInformationLogHistoryAsyn("HandleReceviedMessageAsync:Error", JsonConvert.SerializeObject(webhookDto), JsonConvert.SerializeObject(ex), "processing received message");
                        Console.WriteLine($"Error: {ex.Message}");
                    }
                }
            }
        }

        private async Task<(string textMessage, string url, string mimeType, string caption, string? replyId)> GetMediaDetailsAsync(
            string textMessage,
            string type,
            Message message,
            string phoneNumberId,
            string? replyId)
        {
            string url = string.Empty;
            string mimeType = string.Empty;
            string caption = string.Empty;

            switch (type)
            {
                case "text":
                    textMessage = message.Text?.Body;
                    break;
                case "reaction":
                    textMessage = message.Reaction?.Emoji;
                    replyId = message.Reaction?.MesssageId;
                    break;
                case "image":
                    url = await _mediaURL.GetByMediaId(message.Image?.Id, phoneNumberId);
                    mimeType = message.Image?.MimeType;
                    caption = message.Image?.Caption;
                    break;
                case "video":
                    url = await _mediaURL.GetByMediaId(message.Video?.Id, phoneNumberId);
                    mimeType = message.Video?.MimeType;
                    caption = message.Video?.Caption;
                    break;
                case "audio":
                    url = await _mediaURL.GetByMediaId(message.Audio?.Id, phoneNumberId);
                    mimeType = message.Audio?.MimeType;
                    caption = message.Audio?.Caption;
                    break;
                case "sticker":
                    url = await _mediaURL.GetByMediaId(message.Sticker?.Id, phoneNumberId);
                    mimeType = message.Sticker?.MimeType;
                    caption = message.Sticker?.Caption;
                    break;
                case "document":
                    url = await _mediaURL.GetByMediaId(message.Document?.Id, phoneNumberId);
                    mimeType = message.Document?.MimeType;
                    caption = message.Document?.Caption;
                    break;
                case "button":
                    textMessage = message.Button.Text;
                    break;
                case "interactive":
                    if (message.Interactive.Type == "button_reply")
                    {
                        textMessage = message.Interactive.ButtonReply.Title;

                    }
                    else if (message.Interactive.Type == "list_reply")
                    {
                        textMessage = message.Interactive.ListReply.Title;
                        //textMessage = (list.Title + "\n" + list.Description);

                    }
                    break;
            }
            return (textMessage, url, mimeType, caption, replyId);
        }
        #endregion

        #region send conversation message by socket
        private async Task SendConvMessageOnServer(string businessId, List<Conversations> conversations, bool? isRenderContact = true)
        {
            if (string.IsNullOrEmpty(businessId) || conversations == null || !conversations.Any())
                return;

            try
            {
                var convMessages = conversations.Adapt<List<ConversationDto>>();
                var replyIds = conversations.Where(c => !string.IsNullOrEmpty(c.ReplyId)).Select(c => c.ReplyId).ToList();

                if (replyIds.Any())
                {
                    var repliesConvs = (await _applicationDbContext.Conversations
                        .Where(c => replyIds.Contains(c.WhatsAppMessageId))
                        .ToListAsync()).Adapt<List<ConversationDto>>();

                    var replyDict = repliesConvs.ToDictionary(r => r.WhatsAppMessageId);
                    convMessages.ForEach(conv =>
                    {
                        if (!string.IsNullOrEmpty(conv.ReplyId) && replyDict.TryGetValue(conv.ReplyId, out var replyConv))
                        {
                            conv.Reply = replyConv;
                        }
                    });
                }
                var users = await _applicationDbContext.Users.Where(u => u.CompanyId == businessId).Select(u => new { u.Id }).ToListAsync();
                var groupPrefix = businessId.ToLower();
                var messageTasks = users.Select(user => _messageHub.Clients.Groups($"{groupPrefix}{user.Id.ToString().ToLower()}").ReceiveMessageFromServer(convMessages));
                var contactTasks = Enumerable.Empty<Task>();
                if (isRenderContact ?? false)
                {
                    contactTasks = users.Select(user =>
                        _messageHub.Clients
                            .Groups($"{groupPrefix}{user.Id.ToString().ToLower()}")
                            .RenderContacts()
                    );
                }
                // Run all tasks concurrently
                await Task.WhenAll(messageTasks.Concat(contactTasks));
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn("SendConvMessageOnServer:Error", JsonConvert.SerializeObject(conversations), JsonConvert.SerializeObject(ex), "processing message hub");
                Console.WriteLine($"Error in SendConvMessageOnServer: {ex.Message}");
            }
        }

        #endregion

        #endregion

        private async void SendQualityRatingEmail(string previousQualityScore, string newQualityScore, string businessId)
        {
            var userDetails = await (
                              from user in _applicationDbContext.Users
                              join role in _applicationDbContext.Roles
                              on user.RoleId equals role.Id.ToString()
                              where user.CompanyId == businessId &&
                              role.CompanyId == businessId &&
                              (role.Name == "owner" || role.Name == "admin")
                              select new { user.EmailAddress, user.Name }).ToListAsync();

            var emails = userDetails.Select(i => i.EmailAddress).ToList();
            var clientName = userDetails.Select(i => i.Name).ToList();

            await _emailService.SendQualityRatingEmail(emails, previousQualityScore, newQualityScore, clientName);
        }


    }
}
