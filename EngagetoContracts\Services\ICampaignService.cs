﻿using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace EngagetoContracts.Services
{
    public interface ICampaignService : ITransientService
    {
        Task<bool> CreateAsync(Guid userId, string userName, CreateCampaignDto dto);
        Task<bool> RerunCampaignAsync(Guid userId, string userName, BaseCampaignsDto campaignDto, DateTime? scheduledDate);
        Task<bool> IsExistCampaignNameAsync(string businessId, string name);
        Task<bool> DeleteAsync(string businessId, Guid id);
        Task<Campaign> GetCampaignAsync(string businessId, Guid id);
        Task<List<CampaignMessageCountsDto>> GetCampaignAnalyticsAsync(string businessId, Guid? userId, FilterDto? filter, int? page, int? pageSize);
        Task<List<Campaign>> GetScheduleCampaignAsync(string businessId, Guid? userId, FilterDto? filter, int? page, int? pageSize);
        Task<int> GetCampaignCountAsync(string businessId, CampaignState state, FilterCondition? condition, Search? search);
        Task<DataTable> GetCampaignExportAsync(string businessId, Guid userId, Guid CampaignId);
        Task<List<ViewCampaignAnalyticsDto>> GetAllCampaignAnalyticsAsync(string businessId, FilterDto? filter, int? page, int? pageSize);
        Task<CampaignAnalytsicDto> GetCampaignAnalyticsDetailsAsync(Guid CampaignId);
        Task<FileStreamResult> ExportCampaignByIdAsync(Guid id, CancellationToken cancellationToken);
        Task<CampaignUploadResultDto?> CampaignExcelUploadedFile(UploadFileDto fileDto);
        Task<List<CampaignContactDetailDto>> GetCampaignContactsByStatusStoredProcAsync(CampaignContactFilterDto  campaignContactFilterDto);

    }
}