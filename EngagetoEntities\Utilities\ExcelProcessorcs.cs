﻿using EngagetoEntities.Dtos.ContactDtos;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;
using System.Globalization;
using Quartz.Util;
using System.Data;
using System.Text;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Packaging;

namespace EngagetoEntities.Utilities
{
    public class ExcelProcessorcs
    {
        public Dictionary<string, List<string>> ColumnMapping { get; set; }

        public ExcelProcessorcs(Dictionary<string, List<string>> columnMapping)
        {
            this.ColumnMapping = columnMapping;
        }
        // Example Column Mapping
        public async Task<List<T>> ProcessExcel<T>(string filePath) where T : new()
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                var result = new List<T>();
                using (var httpClient = new HttpClient())
                using (var response = await httpClient.GetAsync(filePath))
                {
                    response.EnsureSuccessStatusCode();

                    using (var stream = await response.Content.ReadAsStreamAsync())
                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[0];
                        var columnMapping = MapExcelColumnsToModel(worksheet);
                        var properties = typeof(T).GetProperties();

                        for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                        {
                            var instance = new T();
                            bool shouldSkipRow = false; // Flag to track if we should skip this row

                            foreach (var columnDetail in columnMapping)
                            {
                                var property = properties.FirstOrDefault(x => x.Name == columnDetail.Key);
                                if (property == null) continue;

                                if (columnMapping.TryGetValue(property.Name, out int colIndex))
                                {
                                    var cellValue = GetCellValue(worksheet, row, colIndex);

                                    // If 'Name' or 'Contact' column is empty, mark row to be skipped
                                    if ((property.Name == "Contact") && string.IsNullOrWhiteSpace(cellValue))
                                    {
                                        shouldSkipRow = true;
                                        break; // Stop processing this row immediately
                                    }
                                    if (!string.IsNullOrEmpty(cellValue))
                                    {
                                        var convertedValue = ConvertToPropertyType(cellValue, property.PropertyType);
                                        if (convertedValue != null && property.CanWrite)
                                        {
                                            property.SetValue(instance, convertedValue);
                                        }
                                    }
                                }
                            }
                            // Only add to result if row is valid
                            if (!shouldSkipRow)
                            {
                                result.Add(instance);
                            }
                        }
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Process Excel file in streaming chunks using yield - never loads entire file into memory
        /// </summary>
        public async IAsyncEnumerable<List<T>> ProcessExcelInChunks<T>(string filePath, int chunkSize = 500) where T : new()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var httpClient = new HttpClient();
            using var response = await httpClient.GetAsync(filePath);
            response.EnsureSuccessStatusCode();

            using var stream = await response.Content.ReadAsStreamAsync();
            using var package = new ExcelPackage(stream);

            var worksheet = package.Workbook.Worksheets[0];
            var columnMapping = MapExcelColumnsToModel(worksheet);
            var properties = typeof(T).GetProperties();
            var totalRows = worksheet.Dimension?.End.Row ?? 0;

            var currentRow = 2;

            while (currentRow <= totalRows)
            {
                var chunk = new List<T>();
                var rowsProcessed = 0;

                while (rowsProcessed < chunkSize && currentRow <= totalRows)
                {
                    var instance = new T();
                    bool shouldSkipRow = false;

                    try
                    {
                        foreach (var columnDetail in columnMapping)
                        {
                            var property = properties.FirstOrDefault(x => x.Name == columnDetail.Key);
                            if (property == null) continue;

                            if (columnMapping.TryGetValue(property.Name, out int colIndex))
                            {
                                var cellValue = GetCellValue(worksheet, currentRow, colIndex);

                                if ((property.Name == "Contact") && string.IsNullOrWhiteSpace(cellValue))
                                {
                                    shouldSkipRow = true;
                                    break;
                                }

                                if (!string.IsNullOrEmpty(cellValue))
                                {
                                    var convertedValue = ConvertToPropertyType(cellValue, property.PropertyType);
                                    if (convertedValue != null && property.CanWrite)
                                    {
                                        property.SetValue(instance, convertedValue);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Optional: log and skip row or rethrow
                        throw new Exception($"Error at row {currentRow}: {ex.Message}", ex);
                    }

                    if (!shouldSkipRow)
                    {
                        chunk.Add(instance);
                    }
                    currentRow++;
                    rowsProcessed++;
                }

                if (chunk.Any())
                {
                    yield return chunk; // ✅ safe outside of try-catch
                }
            }
        }
        private Dictionary<string, int> MapExcelColumnsToModel(ExcelWorksheet worksheet)
        {
            var columnMapping = new Dictionary<string, int>();

            for (int col = 1; col <= worksheet.Dimension.End.Column; col++)
            {
                var columnName = worksheet.Cells[1, col].Text; // Header row value

                foreach (var map in ColumnMapping)
                {
                    if (map.Value.Any(alias => alias.Equals(columnName, StringComparison.OrdinalIgnoreCase)))
                    {
                        columnMapping[map.Key] = col; // Map property name to Excel column index
                        break;
                    }
                }
            }
            return columnMapping;
        }

        private string? GetCellValue(ExcelWorksheet worksheet, int row, int columnIndex)
        {
            var cell = worksheet.Cells[row, columnIndex].Text?.Trim();
            string value = cell;
            // Fix scientific format if needed
            if (value != null && value.Contains("E"))
            {
                if (double.TryParse(value, out double number))
                {
                    value = number.ToString("0", CultureInfo.InvariantCulture);
                }
            }
            return value;

        }

        private object? ConvertToPropertyType(string cellValue, Type propertyType)
        {
            if (string.IsNullOrWhiteSpace(cellValue))
            {
                return null;  // Return null for empty values
            }

            try
            {
                // If it's a basic type, convert accordingly
                if (propertyType == typeof(int))
                {
                    return int.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(decimal))
                {
                    return decimal.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(DateTime))
                {
                    return DateTime.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(bool))
                {
                    return bool.TryParse(cellValue, out var result) ? result : default;
                }
                else if (propertyType == typeof(string))
                {
                    return cellValue;  // Return the value as is for string
                }
                else if (propertyType.IsEnum)  // If the property is an enum, try to parse it
                {
                    return Enum.TryParse(propertyType, cellValue, true, out var enumValue) ? enumValue : null;
                }
                else if (propertyType == typeof(Guid))
                {
                    return Guid.TryParse(cellValue, out var result) ? result : default;
                }
                else
                {
                    // For custom types or other cases, fallback to Convert.ChangeType
                    return Convert.ChangeType(cellValue, propertyType);
                }
            }
            catch (Exception)
            {
                // Handle conversion failure gracefully
                return null;
            }
        }

        public static bool IsValidFile(IFormFile file, out string ext)
        {
            List<string> extList = new List<string> { "xls", "xlsx", "csv" };
            ext = file.FileName.Split(".").LastOrDefault();
            if (extList.Contains(ext.ToLowerInvariant()))
            {
                return true;
            }
            return false;
        }

        public static List<string> GetFileColumns(IFormFile file)
        {
            MemoryStream stream = new MemoryStream();
            file.CopyTo(stream);

            if (stream.Length == 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            if (stream.Position != 0)
            {
                stream.Position = 0;
            }
            List<string> columns = new List<string>();
            var package = new ExcelPackage(stream);
            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            if (worksheet != null)
            {
                var worksheetIndex = worksheet.Index;
                var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
                var noOfRows = worksheet?.Dimension?.Rows ?? 0;
                if (noOfRows > 0 && noOfColumns > 0)
                {
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[1, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                        columns.Add(value);
                    }
                }
            }
            else
            {
                throw new Exception("no sheet available");
            }

            return columns.Distinct().ToList();
        }
        public static Dictionary<string, List<string>> GetFileColumnsOfMultiSheets(IFormFile file)
        {
            MemoryStream stream = new MemoryStream();
            file.CopyTo(stream);

            if (stream.Length == 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            if (stream.Position != 0)
            {
                stream.Position = 0;
            }
            var package = new ExcelPackage(stream);
            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
            Dictionary<string, List<string>> sheetColumns = new Dictionary<string, List<string>>();
            string fileName = Path.GetFileName(file.FileName);
            foreach (var worksheet in package.Workbook.Worksheets)
            {

                if (worksheet != null)
                {
                    List<string> columns = new List<string>();
                    var worksheetIndex = worksheet.Index;
                    var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
                    var noOfRows = worksheet?.Dimension?.Rows ?? 0;
                    if (noOfRows > 0 && noOfColumns > 0)
                    {
                        for (int col = 1; col <= noOfColumns; col++)
                        {
                            var value = worksheet?.Cells[1, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                            columns.Add(value);
                        }
                    }
                    //sheetColumns.Add($"{fileName}/{worksheet.Name}", columns);
                    sheetColumns.Add(worksheet.Name, columns);
                }
            }

            return sheetColumns;
        }
        public static async Task<List<string>> GetCSVColumns(IFormFile file)
        {
            using MemoryStream stream = new MemoryStream();
            await file.CopyToAsync(stream);
            byte[] fileBytes = stream.ToArray();
            string csvData = Encoding.Default.GetString(fileBytes);
            List<string> columns = new List<string>();
            columns = csvData.Split("\r\n").FirstOrDefault().Split('\u002C').ToList();
            return columns;
        }
        public static DataTable CSVToDataTable(MemoryStream stream)
        {
            DataTable dataTable = new DataTable();
            byte[] fileBytes = stream.ToArray();
            string csvData = Encoding.Default.GetString(fileBytes).Trim();


            int rowIndex = 1;
            foreach (string row in csvData.Split("\r\n"))
            {
                if (rowIndex < 2)
                {
                    foreach (var cell in row.Split('\u002C'))
                    {
                        dataTable.Columns.Add(new DataColumn(cell));
                    }
                }
                else
                {
                    var dataRow = dataTable.NewRow();
                    try
                    {
                        int cellIndex = 1;
                        foreach (var cell in row.Split('\u002C'))
                        {
                            dataRow[dataTable.Columns[cellIndex - 1]] = cell;
                            cellIndex++;
                        }
                        dataTable.Rows.Add(dataRow);
                    }
                    catch (Exception ex)
                    {
                        dataTable.Rows.Add(dataRow);
                    }

                }
                rowIndex++;
            }
            return dataTable;
        }

        public static DataTable ConvertExcelToDataTable(Stream fileStream, string? sheetName = null)
        {
            DataTable dt = new();
            if (fileStream == null || fileStream.Length <= 0)
            {
                throw new Exception("No data found in excel sheet.");
            }
            var package = new ExcelPackage(fileStream);
            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
            var sheetCount = package?.Workbook?.Worksheets.Count ?? 0;
            if (sheetCount <= 0) { throw new Exception("No sheet found in the excel File."); }
            ExcelWorksheet? worksheet = null;
            if (!string.IsNullOrWhiteSpace(sheetName))
            {
                string actualSheetName = sheetName.Contains('/') ? sheetName.Split('/').Last() : sheetName;
                worksheet = package?.Workbook?.Worksheets?[actualSheetName];
                // worksheet = package?.Workbook?.Worksheets?[sheetName];
                if (worksheet == null) { throw new Exception($"No sheet found by the name \"{actualSheetName}\"."); }
            }
            else
            {
                worksheet = package?.Workbook?.Worksheets?.FirstOrDefault();
            }
            var noOfColumns = worksheet?.Dimension?.Columns ?? 0;
            var noOfRows = worksheet?.Dimension?.Rows ?? 0;
            for (int row = 1; row <= noOfRows; row++)
            {
                if (row <= 1)
                {
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? $"UnSpecified {col}";
                        dt.Columns.Add(value);
                    }
                }
                else if (row > 1)
                {
                    var dataRow = dt.NewRow();
                    for (int col = 1; col <= noOfColumns; col++)
                    {
                        var value = worksheet?.Cells[row, col]?.Value?.ToString()?.Trim() ?? "";
                        dataRow[col - 1] = value;
                    }
                    dt.Rows.Add(dataRow);
                }
            }
            return dt;
        }

        public static MemoryStream CreateExcelData(List<InvalidContactDto> inValidCustomers)
        {
            using MemoryStream stream = new MemoryStream();


           SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(stream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();

            //Add stylesheet for wrap text and column and row auto height and width
            //var styleSheet = AddStyleSheet(spreadsheetDocument);

            workbookpart.Workbook = new DocumentFormat.OpenXml.Spreadsheet.Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet(new SheetData());
            Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild<Sheets>(new Sheets());
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "mySheet"
            };
            sheets.Append(sheet);
            Worksheet worksheet = worksheetPart.Worksheet;
            SheetData sheetData = worksheet.GetFirstChild<SheetData>();
            List<string> headers = new();
            var obj = inValidCustomers.FirstOrDefault();
            Type objType = obj.GetType();

            System.Reflection.PropertyInfo[] properties = objType.GetProperties();

            foreach (System.Reflection.PropertyInfo property in properties)
            {
                headers.Add(property.Name);
            }
            Row row1 = new Row();
            foreach (var item in headers)
            {
                Cell cell1 = new Cell()
                {
                    CellValue = new CellValue(item),
                    DataType = CellValues.String,

                };
                row1.Append(cell1);
            }
            sheetData.Append(row1);
            foreach (var inValidCustomer in inValidCustomers)
            {
                Row row = new Row();
                CreateCell(row, inValidCustomer.Name);
                CreateCell(row, inValidCustomer.CountryCode);
                CreateCell(row, inValidCustomer.Contact);
                CreateCell(row, inValidCustomer.Email);
                CreateCell(row, inValidCustomer.Source);
                CreateCell(row, inValidCustomer?.Notes ?? null);
                CreateCell(row, inValidCustomer.Errors);
                sheetData.Append(row);
            }
            worksheetPart.Worksheet.Save();
            workbookpart.Workbook.Save();
            spreadsheetDocument.Close();
            return stream;
        }
        public static void CreateCell(Row row, string value, CellValues type = CellValues.String)
        {
            Cell cell = new Cell()
            {
                CellValue = new CellValue(value),
                DataType = type,
            };
            row.Append(cell);
        }
    }
}
