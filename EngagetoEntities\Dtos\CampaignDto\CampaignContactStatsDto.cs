using EngagetoEntities.Enums;

namespace EngagetoEntities.Dtos.CampaignDto
{
    public class CampaignContactFilterDto
    {
        public Guid CampaignId { get; set; }
        public ConvStatus? Status { get; set; }
        public bool IncludeReplies { get; set; }
        public bool IsUndelivered { get; set; } = false;  // New property for Undelivered status
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? BusinessId { get; set; }
    }

    public class CampaignContactDetailDto
    {
        public Guid ContactId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string? Email { get; set; }
        public ChatStatus ChatStatus { get; set; }
        public ConvStatus ConversationStatus { get; set; }

    }


}
