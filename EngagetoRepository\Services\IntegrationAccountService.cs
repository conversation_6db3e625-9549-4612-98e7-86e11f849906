﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.Services;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace EngagetoRepository.Services
{
    public class IntegrationAccountService : IIntegrationAccountService
    {
        private readonly IGenericRepository _genericRepository;
        private readonly IUserIdentityService _userIdentityService;
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IJobService _jobService;
        JsonSerializerSettings options = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            Formatting = Formatting.Indented // optional
        };
        public IntegrationAccountService(IGenericRepository genericRepository, IUserIdentityService userIdentityService, ApplicationDbContext dbContext, ILogHistoryService logHistoryService, IJobService jobTaskService)
        {
            _genericRepository = genericRepository;
            _userIdentityService = userIdentityService;
            _dbContext = dbContext;
            _logHistoryService = logHistoryService;
            _jobService = jobTaskService;
        }
        public async Task<Guid> AddAccountAsync(BaseIntegrationAccountDto accountDto)
        {
            var accont = accountDto.Adapt<IntegrationAccount>();
            accont.Id = Guid.NewGuid();
            if (await _genericRepository.IsExistAsync<IntegrationAccount>(new() { { "Name", accountDto.Name }, { "BusinessId", _userIdentityService.BusinessId } }, "IntegrationAccounts"))
            {
                throw new Exception("Account name is already exist. please choose different name.");
            }
            if (!string.IsNullOrEmpty(accountDto.RawPayload))
            {
                try
                {
                    var jobject = JObject.Parse(accountDto.RawPayload);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException("Raw payload is not valid object.");
                }
            }
            accont.CreatedAt = DateTime.UtcNow;
            accont.UpdatedAt = DateTime.UtcNow;
            accont.CreatedBy = _userIdentityService.UserId;
            accont.UpdatedBy = _userIdentityService.UserId;
            accont.BusinessId = _userIdentityService.BusinessId;
            accont.UserId = _userIdentityService.UserId;
            if (accont.Action == IntegrationAction.CallByExternal)
            {
                accont.APIKey = GenerateApiKey($"{accont.Id.ToString()},{accont.BusinessId}");
            }
            await _dbContext.AddAsync(accont);
            await _dbContext.SaveChangesAsync();
            return accont.Id;
        }

        public async Task<bool> DeleteAccountAsync(Guid accountId)
        {
            var account = (await _genericRepository.GetByObjectAsync<IntegrationAccount>(new() { { "Id", accountId } }))?.FirstOrDefault();
            if (account == null)
            {
                return false;
            }
            account.IsDeleted = false;
            account.DeletedAt = DateTime.UtcNow;
            account.DeletedBy = _userIdentityService.UserId;
            _dbContext.Update(account);
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<List<ViewIntegrationAccountDto>> GetAccountAsync()
        {
            return (await _genericRepository.GetByObjectAsync<IntegrationAccount>(new() { { "IsDeleted", false }, { "BusinessId", _userIdentityService.BusinessId } }))
                .Adapt<List<ViewIntegrationAccountDto>>();
        }

        public async Task<List<ViewIntegrationAccountDto>> GetAccountByIdAsync(Guid accountId)
        {
            return (await _genericRepository.GetByObjectAsync<IntegrationAccount>(new() { { "IsDeleted", false }, { "Id", accountId } }))
                .Adapt<List<ViewIntegrationAccountDto>>();
        }

        public async Task<ViewIntegrationAccountDto> UpdateAccountAsync(EditIntegrationAccountDto editAccount)
        {
            var account = editAccount.Adapt<IntegrationAccount>();
            account.UpdatedAt = DateTime.UtcNow;
            account.UpdatedBy = _userIdentityService.UserId;
            _dbContext.Update(account);
            await _dbContext.SaveChangesAsync();
            return account.Adapt<ViewIntegrationAccountDto>();
        }

        public async Task SyncLeadInBackgroundAsync(Guid businessId, SourceType source)
        {
             _jobService.Enqueue(() => SyncFailedLeadsAsync(businessId, source));
        }

        public async Task<bool> SyncFailedLeadsAsync(Guid businessId, SourceType source)
        {
            try
            {
                var contacts = _dbContext.Contacts
                    .Where(c => c.BusinessId == businessId && c.Source == source && !c.IsSent)
                    .ToList();

                if (!contacts.Any()) return false;

                var eventsToCheck = new[] { IntegrationEvent.LeadGen, IntegrationEvent.OneTimeLeadGen, IntegrationEvent.OneTimeReceived };     

                var integrationAccounts = _dbContext.IntegrationAccounts
                                                    .Where(i => i.BusinessId == businessId.ToString()
                                                    && i.IsActive
                                                    && i.Action == IntegrationAction.CallByExternal)
                                                   .ToList(); 

                var integrationAccount = integrationAccounts
                    .FirstOrDefault(i => i.IntegrationEvents != null && i.IntegrationEvents.Any(e => eventsToCheck.Contains(e)));



                if (integrationAccount == null) return false;

                List<IntegrationEvent> events = new List<IntegrationEvent>();

                events.Add(IntegrationEvent.LeadGen);
                if (events == null || !events.Any())
                {
                    return false;
                }

                if (!events.Any()) return true;

                foreach (var contact in contacts)
                {
                    foreach (var model in integrationAccount?.PayloadDict ?? new())
                    {
                        object? payload = model.Key switch
                        {
                            ModelMapping.Contact => contact,
                            ModelMapping.Conversation => null,
                            ModelMapping.IntegrationPayload => new IntegrationPayloadForExternal
                            {
                                Contact = contact,
                                Conversations = null
                            },
                            _ => null
                        };

                        if (payload != null)
                        {

                            await CallExternalApiForLeadAsync(
                                businessId.ToString(),
                                JObject.FromObject(payload),
                                model.Key,
                                integrationAccount.IntegrationEvents.FirstOrDefault());
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveInformationLogHistoryAsyn(
                    "Error:SyncFailedLeadsAsync",
                    $"Business: {businessId}",
                    ex.Message,
                    ex.StackTrace
                );
                return false;
            }
        }

        public async Task<bool> CallExternalApiForLeadAsync(string businessId, JObject obj, ModelMapping modelMapping, IntegrationEvent @event)
        {
            try
            {
                var accounts = (await _genericRepository.GetByObjectAsync<IntegrationAccount>(
                    new() { { "BusinessId", businessId }, { "IsDeleted", false }, { "IsActive", true } }
                ));
                accounts = accounts.Where(x => x.Action == IntegrationAction.CallByExternal && (x.IntegrationEvents != null && x.IntegrationEvents.Contains(@event))).ToList();
                foreach (var account in accounts)
                {
                    try
                    {
                        if (string.IsNullOrEmpty(account.APIEndpoint))
                        {
                            await _logHistoryService.SaveErrorLogHistoryAsyn("CallExternalApiForLeadAsync", JsonConvert.SerializeObject(account), "", "Integration account is missing or API endpoint is empty.", null);
                            return false;
                        }
                        Dictionary<string, object> keyValues = new Dictionary<string, object>();

                        keyValues = ObjectHelper.ConvertJObjectToDictionary(obj);
                        var payload = account.PayloadDict?.FirstOrDefault(x => x.Key == modelMapping).Value ?? new Dictionary<string, string>();

                        var updatedPayload = payload.ToDictionary(
                                item => item.Key,
                                item =>
                                {
                                    if (!item.Value.Contains("#")) return item.Value;

                                    var key = item.Value.Replace("#", "");
                                    return keyValues.TryGetValue(key, out var value)
                                        ? (value is JArray or IList<object> ? value : value?.ToString() ?? string.Empty)
                                        : string.Empty;
                                }
                            );
                        // external mapping we are using here to mapping the data with target property name
                        if (account?.PropertyMappingList?.Count > 0)
                        {
                            // Normalize updatedPayload.Values
                            var values = updatedPayload.Values
                                .Where(x => x != null) // Ensure no null values
                                .Select(x => Regex.Replace(x.ToString() ?? string.Empty, @"[^a-zA-Z0-9]+", "").ToLowerInvariant())
                                .ToHashSet(); // Use HashSet for faster lookups

                            // Normalize and find match in PropertyMappingList
                            var normalizedText = Regex.Replace("DDJAY-Yamuna", @"[^a-zA-Z0-9]+", "").ToLowerInvariant();
                            Console.WriteLine(normalizedText);

                            var mappingData = account?.PropertyMappingList
                                .FirstOrDefault(x => values.Any(v => v.Contains(
                                    Regex.Replace(x.Key, @"[^a-zA-Z0-9]+", "").ToLowerInvariant())));


                            if (mappingData != null && !string.IsNullOrEmpty(mappingData?.TargetPropertyName))
                            {
                                try
                                {
                                    updatedPayload[mappingData.TargetPropertyName] = mappingData.Value;
                                }catch(Exception ex) 
                                {
                                    await _logHistoryService.SaveErrorLogHistoryAsyn("CallExternalApiForLeadAsync", JsonConvert.SerializeObject(updatedPayload),businessId,ex.Message,ex.StackTrace);
                                }
                            }
                            else
                            {
                                foreach (var mapping in account?.PropertyMappingList ?? new List<PropertyMappingDto>())
                                {
                                    var token = obj.TryGetValue(mapping.Key, out var valueToken) ? valueToken : null;

                                    if (token != null)
                                    {
                                        if (token.Type == JTokenType.Object || token.Type == JTokenType.Array)
                                        {
                                            // Directly assign the raw JToken — keeps format (string, number, array, object)
                                            updatedPayload[mapping.TargetPropertyName] = ObjectHelper.ConvertToCamelCase(token);
                                        }
                                        else
                                        {
                                            updatedPayload[mapping.TargetPropertyName] = token.ToString();
                                        }
                                    }
                                    else
                                    {
                                        updatedPayload[mapping.TargetPropertyName] = mapping.Value ?? "";
                                    }
                                }
                            }

                        }
                        var updatedObj = ObjectHelper.ConvertToJObject(updatedPayload);

                        using var client = new HttpClient();

                        // Add headers
                        if (account.HeadersDict?.Count > 0)
                        {
                            foreach (var item in account.HeadersDict)
                            {
                                client.DefaultRequestHeaders.Add(item.Key, item.Value);
                            }
                        }

                        // Add path or query parameters
                        account.APIEndpoint = AddPathOrQueryParameters(account.APIEndpoint, account.HeaderParameterObj);

                        // Prepare HTTP content based on the expected format
                        HttpContent content = account.AcceptedFormat switch
                        {
                            DataFormat.Json => new StringContent(JsonConvert.SerializeObject(updatedObj,options), Encoding.UTF8, "application/json"),
                            DataFormat.FormUrlEncoded or DataFormat.FormData =>
                                new FormUrlEncodedContent(ConvertJsonToFormUrlEncoded(updatedPayload)),
                            _ => new StringContent(JsonConvert.SerializeObject(updatedObj,options), Encoding.UTF8, "application/json")
                        };

                        // Make the API call
                        
                        HttpResponseMessage response = account.APIMethod switch
                        {
                            ApiMethod.Get => await client.GetAsync(account.APIEndpoint),
                            ApiMethod.Post => await client.PostAsync(account.APIEndpoint, content),
                            ApiMethod.Patch => await client.PatchAsync(account.APIEndpoint, content),
                            _ => throw new NotSupportedException($"Unsupported API method: {account.APIMethod}")
                        };

                        if (response.IsSuccessStatusCode)
                        
                        {
                            var contactObj = obj["Contact"];
                            if (contactObj != null)
                            {
                                string contactIdStr = contactObj["ContactId"]?.ToString() ?? "";
                                 
                                if (Guid.TryParse(contactIdStr, out Guid contactId))
                                {
                                    var contact = _dbContext.Contacts.FirstOrDefault(i => i.ContactId == contactId);
                                    if (contact != null)
                                    {
                                        contact.IsSent = true;
                                        await _dbContext.SaveChangesAsync();
                                    }
                                }
                                else
                                {
                                    await _logHistoryService.SaveInformationLogHistoryAsyn(
                                        "ProcessFailedLeadGenerationAsync",
                                        contactIdStr,
                                        null,
                                        "Invalid GUID format for ContactId");
                                }
                            }
                        }

                        // Log response status
                        await _logHistoryService.SaveInformationLogHistoryAsyn("CallExternalApiForLeadAsync", JsonConvert.SerializeObject(updatedObj), JsonConvert.SerializeObject(await response.Content.ReadAsStringAsync()), $"API call to {account.APIEndpoint} returned status {response.StatusCode}.");
                    }
                    catch (Exception ex)
                    {
                        await _logHistoryService.SaveErrorLogHistoryAsyn("CallExternalApiForLeadAsync", JsonConvert.SerializeObject(obj), $"{businessId}", ex.Message, ex.StackTrace);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("CallExternalApiForLeadAsync", JsonConvert.SerializeObject(obj), $"{businessId}", ex.Message, ex.StackTrace);
                return false;
            }
        }
        public async Task<IntegrationWebhookPayloadDto> GetPayloadPropertyMappingAsync(string accountName)
        {
            try
            {
                var accont = await _dbContext.IntegrationAccounts.FirstOrDefaultAsync(x => x.Name == accountName && x.BusinessId == _userIdentityService.BusinessId);
                if (accont == null) throw new InvalidOperationException("accont not found");
                var payloadMapping = new IntegrationWebhookPayloadDto();
                payloadMapping.BusinessId = accont.BusinessId;
                if (!string.IsNullOrEmpty(accont.RawPayload))
                {
                    var jobject = JObject.Parse(accont.RawPayload);
                    var dict = ObjectHelper.ConvertJObjectToDictionary(JObject.Parse(accont.RawPayload));
                    payloadMapping.Payload = new Dictionary<string, List<string>>();
                    payloadMapping.Payload.Add(accountName, dict.Select(x => x.Key).ToList());
                }
                payloadMapping.Contact = (StringHelper.GetPropertyNames<Contacts>()).Select(x => $"#{x}#").ToList();
                payloadMapping.Conversation = (StringHelper.GetPropertyNames<Conversations>()).Select(x => $"#{x}#").ToList();
                var integrationPayload = new IntegrationPayloadForExternal()
                {
                    Contact = new(),
                    Conversations = new()
                };
                payloadMapping.IntegrationPayloadForExternal = ObjectHelper.ConvertJObjectToDictionary(JObject.FromObject(integrationPayload)).Select(x => $"#{x.Key}#").ToList();

                return payloadMapping;

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// Adds path or query parameters to the API endpoint.
        /// </summary>
        private string AddPathOrQueryParameters(string apiEndpoint, HeaderParameterDto headerParams)
        {
            if (headerParams?.Headers == null || headerParams.Headers.Count == 0)
                return apiEndpoint;

            return headerParams.Parameter switch
            {
                HeaderParameter.Path => $"{apiEndpoint}/{string.Join("/", headerParams.Headers.Values)}",
                HeaderParameter.Query => apiEndpoint.Contains('?')
                    ? $"{apiEndpoint}&{string.Join("&", headerParams.Headers.Select(p => $"{p.Key}={p.Value}"))}"
                    : $"{apiEndpoint}?{string.Join("&", headerParams.Headers.Select(p => $"{p.Key}={p.Value}"))}",
                _ => apiEndpoint
            };
        }



        #region helper method
        private string GenerateApiKey(string key)
        {
            byte[] textBytes = Encoding.UTF8.GetBytes(key);

            string base64String = Convert.ToBase64String(textBytes);
            return base64String;
        }

        public static Dictionary<string, string> ConvertJsonToFormUrlEncoded(Dictionary<string,object> dict)
        {
            //var dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonPayload);
            var formData = new Dictionary<string, string>();

            foreach (var kvp in dict)
            {
                if (kvp.Value is JArray array)
                {
                    formData[kvp.Key] = string.Join(",", array.ToObject<string[]>());
                }
                else
                {
                    formData[kvp.Key] = kvp.Value?.ToString() ?? string.Empty;
                }
            }
            return formData;
        }
        #endregion helper method
    }
}
