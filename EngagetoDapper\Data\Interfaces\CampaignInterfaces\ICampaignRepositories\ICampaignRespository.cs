﻿using EngagetoEntities.Dtos.CampaignDto;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;

namespace EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignRepositories
{
    public interface ICampaignRespository
    {
        Task<IEnumerable<ConverstationDetailsDto>> GetConversationDetailsAsync(string CompanyId);
        Task<int> GetCampaignCountAsync(string companyId, CampaignState state, FilterCondition? condition, Search? search);
        Task<bool> BulkUpdateCampaignTrackerAsync(List<CampaignTracker> campaignTrackers);
        Task<List<CampaignContactDetailDto>> GetContactsFromCampaign(CampaignContactFilterDto campaignContactFilterDto);
       } 
}
