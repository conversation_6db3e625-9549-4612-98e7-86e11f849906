﻿using EngagetoEntities.Dtos.CampaignDto;

namespace EngagetoEntities.Dtos.FilterDtos
{
    public class FilterDto
    {
        public Search? Searching { get; set; }
        public Sort? Sorting { get; set; }
        public FilterGroup? Filtering { get; set; }
    }
    public class FilterDataDto
    {
        public FilterCondition? Condition { get; set; }
        public Search? Search { get; set; }
    }
   
    public class Search
    {
        public string? Column { get; set; }
        public string? Value { get; set; }
    }
    public class Sort
    {
        public string? Column { get; set; }
        public string? Order { get; set; }
    }

    public class FilterGroup
    {
        public string? FilterType { get; set; }
        public List<FilterCondition>? Conditions { get; set; }
    }

    public class FilterCondition
    {
        public string? Column { get; set; }
        public string? Operator { get; set; }
        public string? Value { get; set; }
        public string? LogicalOperator { get; set; } 
    }
}
