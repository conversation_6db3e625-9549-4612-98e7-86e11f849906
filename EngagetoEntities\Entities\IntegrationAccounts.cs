﻿using EngagetoEntities.Dtos.IntegrationDtos;
using EngagetoEntities.Enums;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoEntities.Entities
{
    [Table("IntegrationAccounts")]
    public class IntegrationAccount : BaseEntity
    {
        [Key]
        public Guid Id { get; set; }
        public string BusinessId { get; set; }
        public Guid? UserId { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public IntegrationAction Action { get; set; }
        public SourceType Source { get; set; }
        public string? SubSource { get; set; }
        public string? APIEndpoint { get; set; }
        public string? APIKey { get; set; }
        public ApiMethod APIMethod { get; set; }
        public string? RawPayload { get; set; }
        public string? Payload { get; set; }
        public string? Headers { get; set; }
        public DataFormat? AcceptedFormat { get; set; }
        public string? HeaderParameter { get; set; }
        public string? Events { get; set; }
        public string? PropertyMapping { get; set; }
        public bool? IsStopAutoReplyMessage { get; set; } = false;
        public bool? IsStopAutoReplyMessageForCampaign { get; set; } = true;
        public int? StopAutoReplyTimeWindow { get; set; } = 5;

        [NotMapped]
        public List<IntegrationEvent>? IntegrationEvents
        {
            get => !string.IsNullOrEmpty(Events) ? JsonConvert.DeserializeObject<List<IntegrationEvent>>(Events) : null;
            set => Events = value != null ? JsonConvert.SerializeObject(value) : null;
        }

        [NotMapped]
        public Dictionary<string, string>? HeadersDict
        {
            get => !string.IsNullOrEmpty(Headers) ? JsonConvert.DeserializeObject<Dictionary<string, string>>(Headers) : new();
            set => Headers = value != null ? JsonConvert.SerializeObject(value) : null;
        }

        [NotMapped]
        public HeaderParameterDto? HeaderParameterObj
        {
            get => !string.IsNullOrEmpty(HeaderParameter) ? JsonConvert.DeserializeObject<HeaderParameterDto>(HeaderParameter) : new();
            set => HeaderParameter = value != null ? JsonConvert.SerializeObject(value) : null;
        }

        [NotMapped]
        public Dictionary<ModelMapping, Dictionary<string, string>>? PayloadDict
        {
            get => !string.IsNullOrEmpty(Payload) ? JsonConvert.DeserializeObject<Dictionary<ModelMapping, Dictionary<string, string>>>(Payload) : new();
            set => Payload = value != null ? JsonConvert.SerializeObject(value) : null;
        }
        
        [NotMapped]
        public List<PropertyMappingDto>? PropertyMappingList
        {
            get => !string.IsNullOrEmpty(PropertyMapping)
                ? JsonConvert.DeserializeObject<List<PropertyMappingDto>>(PropertyMapping)
                : new List<PropertyMappingDto>();
            set => PropertyMapping = value != null ? JsonConvert.SerializeObject(value) : null;
        }
    }
}
